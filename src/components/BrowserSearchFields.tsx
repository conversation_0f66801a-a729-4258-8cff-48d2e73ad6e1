import {
  DialogButton,
  Dropdown,
  DropdownOption,
  Focusable,
  gamepadDialogClasses,
  PanelSectionRow,
  TextField,
} from "decky-frontend-lib";
import { useEffect, useMemo, memo } from "react";
import { TiRefreshOutline } from "react-icons/ti";
import { ThemeQueryRequest } from "../apiTypes";
import { genericGET } from "../api";
import { useGlobalState } from "../state";
import { FilterDropdownCustomLabel } from "./FilterDropdownCustomLabel";

export function BrowserSearchFields({
  searchOpts,
  searchOptsVarName,
  prevSearchOptsVarName,
  unformattedFilters,
  unformattedFiltersVarName,
  onReload,
  requiresAuth = false,
  getTargetsPath,
}: {
  searchOpts: ThemeQueryRequest;
  searchOptsVarName: string;
  prevSearchOptsVarName: string;
  unformattedFilters: { filters: string[]; order: string[] };
  unformattedFiltersVarName: string;
  getTargetsPath: string;
  requiresAuth?: boolean;
  onReload: () => void;
}) {
  const { setGlobalState } = useGlobalState();

  async function getThemeTargets() {
    genericGET(`${getTargetsPath}`, requiresAuth).then((data) => {
      if (data?.filters) {
        setGlobalState(unformattedFiltersVarName, {
          filters: data.filters,
          order: data.order,
        });
      }
    });
  }

  const formattedFilters = useMemo<{
    filters: DropdownOption[];
    order: DropdownOption[];
  }>(
    () => ({
      filters: [
        {
          data: "All",
          label: (
            <FilterDropdownCustomLabel
              filterValue="All"
              itemCount={
                Object.values(unformattedFilters.filters).reduce(
                  (prev, cur) => prev + Number(cur),
                  0
                ) || ""
              }
            />
          ),
        },
        ...Object.entries(unformattedFilters.filters)
          .filter(([_, itemCount]) => Number(itemCount) > 0)
          .map(([filterValue, itemCount]) => ({
            data: filterValue,
            label: (
              <FilterDropdownCustomLabel
                filterValue={filterValue}
                itemCount={itemCount}
              />
            ),
          })),
      ],
      order: unformattedFilters.order.map((e) => ({ data: e, label: e })),
    }),
    [unformattedFilters]
  );
  useEffect(() => {
    if (unformattedFilters.filters.length < 2) {
      getThemeTargets();
    }
  }, []);

  const repoOptions: never[] = [];
  return (
    <>
      <PanelSectionRow>
        <Focusable style={{ display: "flex", maxWidth: "100%" }}>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              maxWidth: repoOptions.length <= 1 ? "40%" : "33%",
              minWidth: repoOptions.length <= 1 ? "40%" : "33%",
            }}
          >
            <span>Sort</span>
            <Dropdown
              menuLabel="Sort"
              rgOptions={formattedFilters.order}
              strDefaultLabel="Last Updated (Newest)"
              selectedOption={searchOpts.order}
              onChange={(e) => {
                setGlobalState(prevSearchOptsVarName, searchOpts);
                setGlobalState(searchOptsVarName, {
                  ...searchOpts,
                  order: e.data,
                });
              }}
            />
          </div>
          <div
            className="CSSLoader_FilterDropDown_Container"
            style={{
              display: "flex",
              flexDirection: "column",
              maxWidth: repoOptions.length <= 1 ? "40%" : "33%",
              minWidth: repoOptions.length <= 1 ? "40%" : "33%",
              marginLeft: "auto",
            }}
          >
            <span>Filter</span>
            <Dropdown
              menuLabel="Filter"
              rgOptions={formattedFilters.filters}
              strDefaultLabel="All"
              selectedOption={searchOpts.filters}
              onChange={(e) => {
                setGlobalState(prevSearchOptsVarName, searchOpts);
                setGlobalState(searchOptsVarName, {
                  ...searchOpts,
                  filters: e.data,
                });
              }}
            />
            <style>
              {/* The CSS Selector god has done it again */}
              {`
                .CSSLoader_FilterDropDown_Container > button > div > div {
                  width: 100%;
                  display: flex;
                  align-items: start;
                }
                .CSSLoader_FilterDropDown_Container > button > div > .${gamepadDialogClasses.Spacer} {
                  width: 0;
                }
              `}
            </style>
          </div>
        </Focusable>
      </PanelSectionRow>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <Focusable
          style={{ display: "flex", alignItems: "center", width: "96%" }}
        >
          <div style={{ minWidth: "55%", marginRight: "auto" }}>
            <TextField
              label="Search"
              value={searchOpts.search}
              onChange={(e) => {
                setGlobalState(prevSearchOptsVarName, searchOpts);
                setGlobalState(searchOptsVarName, {
                  ...searchOpts,
                  search: e.target.value,
                });
              }}
            />
          </div>
          <DialogButton
            onClick={onReload}
            style={{
              maxWidth: "20%",
              height: "50%",
            }}
          >
            <TiRefreshOutline style={{ transform: "translate(0, 2px)" }} />
            <span>Refresh</span>
          </DialogButton>
        </Focusable>
      </div>
    </>
  );
}

export const MemoizedSearchFields = memo(BrowserSearchFields);
