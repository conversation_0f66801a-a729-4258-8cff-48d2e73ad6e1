{"name": "SDH-AudioLoader", "version": "1.6.0", "description": "Replaces and adds Steam Deck game UI sounds", "scripts": {"build": "shx rm -rf dist && rollup -c", "watch": "rollup -c -w", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/EMERALD0874/SDH-AudioLoader.git"}, "keywords": ["decky", "plugin", "steam-deck", "deck"], "author": "EMERALD0874", "license": "GPL-2.0-or-later", "bugs": {"url": "https://github.com/EMERALD0874/SDH-AudioLoader/issues"}, "homepage": "https://github.com/EMERALD0874/SDH-AudioLoader#readme", "devDependencies": {"@rollup/plugin-commonjs": "^21.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.3.3", "@types/react": "16.14.0", "@types/webpack": "^5.28.0", "rollup": "^2.77.1", "rollup-plugin-import-assets": "^1.1.1", "rollup-plugin-styles": "^4.0.0", "shx": "^0.3.4", "tslib": "^2.4.0", "typescript": "^4.7.4"}, "dependencies": {"@types/lodash": "^4.14.191", "decky-frontend-lib": "^3.21.1", "lodash": "^4.17.21", "react-icons": "^4.4.0"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["react", "react-dom"]}}}